# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo for Ninebot's e-commerce platform with two main applications:
- **H5 App** (`apps/h5/`): Mobile-first e-commerce application using Ant Design Mobile
- **Web App** (`apps/web/`): Desktop e-commerce application using Ant Design

## Development Commands

### Root Level Commands
```bash
# Development
pnpm dev              # Start both apps in development mode
pnpm build            # Build both apps
pnpm lint             # Run linting on both apps
pnpm type-check       # Run TypeScript type checking
pnpm format           # Format code with Prettier
pnpm clean            # Clean build artifacts
pnpm clean:cache      # Clean cache files

# Code Generation
pnpm codegen:dev      # Generate GraphQL types for development
pnpm codegen:prod     # Generate GraphQL types for production
```

### App-Specific Commands
```bash
# H5 App (Mobile)
cd apps/h5
pnpm dev              # Start on port 3001
pnpm build            # Build for production
pnpm deploy:master    # Deploy to master environment
pnpm deploy:slave     # Deploy to slave environment

# Web App (Desktop)
cd apps/web
pnpm dev              # Start on default port
pnpm build            # Build for production
pnpm deploy:master    # Deploy to master environment
pnpm deploy:slave     # Deploy to slave environment
```

## Architecture

### Monorepo Structure
- **Turborepo**: Build system with caching and pipeline optimization
- **pnpm workspace**: Package management with shared dependencies
- **Shared packages**: Core logic in `packages/` directory

### Key Technologies
- **Framework**: Next.js 14 with App Router
- **State Management**: Redux Toolkit with RTK Query
- **UI Libraries**: Ant Design (web), Ant Design Mobile (h5)
- **Styling**: Tailwind CSS with custom configuration
- **Internationalization**: next-intl with Chinese support
- **Authentication**: Clerk integration
- **API Layer**: GraphQL with code generation
- **Caching**: Redis with custom cache handler

### Core Architecture Patterns

#### State Management
- Redux Toolkit store with multiple slices:
  - `globalSlice`: Global application state
  - `userSlice`: User authentication and profile
  - `cartSlice`: Shopping cart functionality
  - `userAddressSlice`: User address management
  - `userOrderSlice`: Order management
  - `checkoutSlice`: Checkout process
- RTK Query for API state management
- Custom middleware for user authentication

#### API Layer
- GraphQL queries and mutations in `packages/core/src/graphql/`
- Auto-generated TypeScript types from GraphQL schema
- Custom API clients for different domains (cart, user, products, etc.)
- Centralized error handling and caching

#### Internationalization
- next-intl middleware for locale detection and routing
- Chinese (`zh-Hans`) as primary language
- Locale-specific messages in `packages/core/src/i18n/messages/`

#### Component Architecture
- **Business Components**: Domain-specific components in `businessComponents/`
- **Common Components**: Reusable UI components in `components/common/`
- **Icons**: Centralized icon components in `components/icons/`

## Key Features

### Multi-Device Support
- Web app automatically redirects mobile users to H5 app
- Responsive design with Tailwind CSS
- Device-specific UI components

### Authentication & Security
- Clerk integration for authentication
- Protected route middleware
- QUID (Unique Identifier) generation for tracking
- Token-based authentication with cookies

### E-commerce Functionality
- Product catalog with search and filtering
- Shopping cart with persistent storage
- Multi-step checkout process
- Payment integration (Alipay, WeChat Pay, UnionPay)
- Order management and tracking
- Customer account management
- Address management
- Coupon system

### Performance Optimization
- Redis caching for API responses
- Image optimization with Next.js
- Code splitting and lazy loading
- Performance monitoring in StoreSelectorModal

## Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow ESLint configuration in `packages/eslint-config/`
- Format with Prettier and Tailwind CSS plugin
- Use Tailwind utility classes for styling

### Component Development
- Prefer functional components with hooks
- Use existing component patterns and naming conventions
- Follow the established file structure:
  - `index.ts` for exports
  - `styles.ts` for component-specific styles
  - Separate icons into individual files

### API Integration
- Use the existing GraphQL queries and mutations
- Add new GraphQL operations to the appropriate files
- Run code generation after adding new GraphQL operations
- Use RTK Query hooks for data fetching

### State Management
- Use Redux Toolkit slices for complex state
- Prefer RTK Query for server state
- Use existing slices when possible
- Follow the established middleware patterns

### Internationalization
- Use the `useTranslations` hook from next-intl
- Add new translation keys to the appropriate locale files
- Follow the established key naming conventions

## Environment Configuration

### Environment Variables
- Configuration is environment-specific (dev, staging, prod)
- Use `.env.development`, `.env.staging`, `.env.production` files
- Sensitive configuration should use environment variables

### Deployment
- PM2 for process management
- Nginx for reverse proxy
- Automated deployment scripts for different environments

## Testing

Currently no formal test framework is configured. When adding tests:
- Consider Jest for unit testing
- Use React Testing Library for component testing
- Follow the established file naming conventions for test files

## Common Issues

### Build Issues
- Run `pnpm clean` and `pnpm install` to resolve dependency issues
- Check GraphQL code generation if types are missing
- Verify environment variables are properly set

### Development Issues
- Ensure both apps are running with `pnpm dev`
- Check middleware configuration for routing issues
- Verify Redis connection for caching issues