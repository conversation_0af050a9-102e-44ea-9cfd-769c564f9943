'use client'
import { useState } from 'react'
import { Modal as AntdModal, ModalProps } from 'antd'

import { Clear } from '@/components'

// 带有hover效果的关闭按钮组件
function CloseButton() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className="cursor-pointer transition-all duration-200">
      <Clear fill={isHovered ? '#da291c' : 'black'} width={24} height={24} />
    </div>
  )
}

export default function Modal({
  isOpen,
  onClose,
  onConfirm,
  title,
  children,
  okText = '确定',
  cancelText = '取消',
  width = 800,
  modalProps,
  okButtonProps,
  cancelButtonProps,
  footer,
  confirmLoading,
  styles,
}: {
  isOpen: boolean
  onClose: () => void
  onConfirm?: () => void
  title: string
  children: React.ReactNode
  okText?: string
  cancelText?: string
  width?: number
  modalProps?: Omit<
    React.ComponentProps<typeof AntdModal>,
    'open' | 'onCancel' | 'onOk' | 'title' | 'okText' | 'cancelText' | 'width'
  >
  okButtonProps?: React.ComponentProps<typeof AntdModal>['okButtonProps']
  cancelButtonProps?: React.ComponentProps<typeof AntdModal>['cancelButtonProps']
  footer?: React.ReactNode
  confirmLoading?: boolean
  styles?: ModalProps['styles']
}) {
  return (
    <AntdModal
      className="custom_modal"
      title={title}
      closeIcon={<CloseButton />}
      zIndex={1002}
      open={isOpen}
      onCancel={onClose}
      onOk={onConfirm}
      okText={okText}
      cancelText={cancelText}
      width={width}
      centered
      maskClosable={false}
      okButtonProps={okButtonProps}
      cancelButtonProps={cancelButtonProps}
      footer={footer}
      confirmLoading={confirmLoading}
      styles={styles}
      {...modalProps}>
      {children}
    </AntdModal>
  )
}
Modal.displayName = 'Modal'
