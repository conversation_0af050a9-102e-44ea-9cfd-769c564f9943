import localFont from 'next/font/local'
import clsx from 'clsx'

export const miSansRegular330 = localFont({
  src: './MiSans-Regular.woff2',
  display: 'block',
  style: 'normal',
  preload: true,
  variable: '--font-family-miSansRegular330',
  fallback: ['-apple-system', 'Helvetica', 'PingFang SC', 'Microsoft Yahei', 'sans-serif'],
})

export const miSansMedium380 = localFont({
  src: './MiSans-Medium.woff2',
  display: 'block',
  style: 'normal',
  preload: true,
  variable: '--font-family-miSansMedium380',
  fallback: ['-apple-system', 'Helvetica', 'PingFang SC', 'Microsoft Yahei', 'sans-serif'],
})

export const miSansDemiBold450 = localFont({
  src: './MiSans-Demibold.woff2',
  display: 'block',
  style: 'normal',
  preload: true,
  variable: '--font-family-miSansDemiBold450',
  fallback: ['-apple-system', 'Helvetica', 'PingFang SC', 'Microsoft Yahei', 'sans-serif'],
})

export const miSansBold630 = localFont({
  src: './MiSans-Bold.woff2',
  display: 'block',
  style: 'normal',
  preload: true,
  variable: '--font-family-miSansBold630',
  fallback: ['-apple-system', 'Helvetica', 'PingFang SC', 'Microsoft Yahei', 'sans-serif'],
})

export const miSansSemibold520 = localFont({
  src: './MiSans-Semibold.woff2',
  display: 'block',
  style: 'normal',
  preload: true,
  variable: '--font-family-miSansSemibold520',
  fallback: ['-apple-system', 'Helvetica', 'PingFang SC', 'Microsoft Yahei', 'sans-serif'],
})

/**
 * 导出字体定义的 Variable
 */
export const miSansFamilyVariable = clsx(
  miSansRegular330.variable,
  miSansMedium380.variable,
  miSansDemiBold450.variable,
  miSansBold630.variable,
  miSansSemibold520.variable,
)
