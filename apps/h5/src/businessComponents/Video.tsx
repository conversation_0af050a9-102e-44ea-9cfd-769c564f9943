'use client'
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import { generateOSSUrl } from '@ninebot/core'
import { Image } from 'antd-mobile'

//图标
const IconVolumeMute = () => (
  <svg width={18} height={18} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path opacity="0.01" d="M16.125 6.75H11.25V11.625H16.125V6.75Z" fill="#fff" />
    <path
      d="M16.6432 11.0008L12.0007 6.3583"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.6016 6.3999L12.0423 10.9592"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 2.25V15.75C6.375 15.75 4.42444 12.3147 4.42444 12.3147H2.25C1.83579 12.3147 1.5 11.9789 1.5 11.5647V6.37905C1.5 5.96482 1.83579 5.62905 2.25 5.62905H4.42444C4.42444 5.62905 6.375 2.25 9 2.25Z"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinejoin="round"
    />
  </svg>
)
const IconVolumeUnmute = () => (
  <svg width={18} height={18} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9 2.25V15.75C6.375 15.75 4.42444 12.3147 4.42444 12.3147H2.25C1.83579 12.3147 1.5 11.9789 1.5 11.5647V6.37905C1.5 5.96482 1.83579 5.62905 2.25 5.62905H4.42444C4.42444 5.62905 6.375 2.25 9 2.25Z"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinejoin="round"
    />
    <path
      d="M12 5.625C12.2337 5.83369 12.4455 6.06739 12.6315 6.32205C13.177 7.0689 13.5 7.99586 13.5 9C13.5 9.99544 13.1826 10.915 12.6457 11.6585C12.4563 11.9207 12.2397 12.161 12 12.375"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.8384 15.4447C15.0313 14.1358 16.4999 11.7394 16.4999 9.00004C16.4999 6.30323 15.0765 3.93885 12.94 2.61719"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
    />
  </svg>
)
const IconPlay = generateOSSUrl('/icons/play.png')

interface VideoProps {
  video_url: string
  poster?: string
  initialPaused?: boolean // 初始暂停状态
  onClick?: () => void // 对应 handleActionPress
  withMute?: boolean // 是否显示静音按钮
  initialMuted?: boolean // 初始静音状态
}

export interface VideoRef {
  play: () => void
  pause: () => void
  mute: () => void
  unmute: () => void
  isPaused: boolean
}

const Video = forwardRef<VideoRef, VideoProps>(
  (
    { video_url, poster, initialPaused = true, onClick, withMute = false, initialMuted = false },
    ref,
  ) => {
    const videoRef = useRef<HTMLVideoElement>(null)
    const [isPaused, setIsPaused] = useState(initialPaused)
    const [isMuted, setIsMuted] = useState(initialMuted)
    const [isLoaded, setIsLoaded] = useState(false)

    const playVideo = useCallback(() => {
      if (videoRef.current) {
        videoRef.current.play().catch((error) => {
          console.error('Video play failed:', error)
          setIsPaused(true) // 播放失败则保持暂停状态
        })
        setIsPaused(false)
      }
    }, [])

    const pauseVideo = useCallback(() => {
      if (videoRef.current) {
        videoRef.current.pause()
        setIsPaused(true)
      }
    }, [])

    const togglePlayPause = useCallback(
      (e?: React.MouseEvent) => {
        if (e) {
          e.stopPropagation()
        }
        if (isPaused) {
          playVideo()
          // 点击播放按钮时，取消静音（参考 RN 逻辑）
          // setIsMuted(false); // 决定是否需要此行为
        } else {
          pauseVideo()
        }
      },
      [isPaused, playVideo, pauseVideo],
    )

    const handleOverlayClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation()
        // 点击覆盖层暂停视频并执行外部 onClick (如果提供)
        pauseVideo()
        if (onClick) {
          onClick()
        }
      },
      [onClick, pauseVideo],
    )

    const toggleMute = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation()
        const nextMuted = !isMuted
        setIsMuted(nextMuted)
        if (videoRef.current) {
          videoRef.current.muted = nextMuted
        }
        // 如果取消静音，则尝试播放 (参考 RN 逻辑)
        if (!nextMuted) {
          playVideo()
        }
      },
      [isMuted, playVideo],
    )

    const handleLoadedData = useCallback(() => {
      setIsLoaded(true)
      if (videoRef.current) {
        // 初始静音
        videoRef.current.muted = true
        setIsMuted(true)

        if (!initialPaused) {
          playVideo()
        }
      }
    }, [initialPaused, playVideo])

    // 暴露控制方法
    useImperativeHandle(
      ref,
      () => ({
        play: () => {
          playVideo()
        },
        pause: () => {
          pauseVideo()
          // setIsMuted(true); // 参考 RN 逻辑，暂停时也静音
        },
        mute: () => {
          setIsMuted(true)
          if (videoRef.current) videoRef.current.muted = true
        },
        unmute: () => {
          setIsMuted(false)
          if (videoRef.current) videoRef.current.muted = false
        },
        isPaused: isPaused,
      }),
      [isPaused, playVideo, pauseVideo],
    )

    // 处理页面可见性变化
    useEffect(() => {
      const handleVisibilityChange = () => {
        if (document.hidden) {
          pauseVideo()
        }
        // 可选：当页面重新可见时是否恢复播放
        // else if (!isPaused) {
        //   playVideo();
        // }
      }
      document.addEventListener('visibilitychange', handleVisibilityChange)
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    }, [pauseVideo])

    return (
      <div className="group relative h-full w-full bg-black" onClick={handleOverlayClick}>
        <video
          ref={videoRef}
          className="block h-full w-full object-contain"
          src={video_url}
          poster={poster}
          preload="auto"
          loop // 保持循环播放
          playsInline // 移动端行内播放
          muted={isMuted} // 受控静音
          controls={false} // 不显示默认控件
          onLoadedData={handleLoadedData}
          // autoPlay
        />
        {/* 覆盖层 */}
        <div className="absolute inset-0 flex items-center justify-center">
          {/* 静音按钮 (如果启用) */}
          {withMute && (
            <button
              className="absolute bottom-8 left-8 z-50 rounded-full p-base text-white"
              onClick={toggleMute}
              aria-label={isMuted ? 'Unmute video' : 'Mute video'}
              aria-pressed={isMuted}>
              {isMuted ? <IconVolumeMute /> : <IconVolumeUnmute />}
            </button>
          )}

          {/* 播放按钮 (仅在暂停且加载完成后显示) */}
          {isLoaded && (
            <button className="z-50 h-[56px] w-[56px] rounded-full" onClick={togglePlayPause}>
              {isPaused && (
                <Image
                  className="play-icon"
                  placeholder=""
                  src={IconPlay}
                  alt="Play"
                  width={56}
                  height={56}
                />
              )}
            </button>
          )}
        </div>
      </div>
    )
  },
)

Video.displayName = 'Video'

export default Video
