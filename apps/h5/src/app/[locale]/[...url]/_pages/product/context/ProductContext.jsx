import {
  appLocalStorage,
  PRECISE_RATE_LIMIT,
  resetCheckout,
  useAddToCartMutation,
  useAuth,
  useBuyNowCheckoutMutation,
  useRateLimitHandler,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useCheckoutCart } from '@ninebot/core/src/businessHooks'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { TRACK_EVENT } from '@ninebot/core/src/constants'
import { ROUTE } from '@ninebot/core/src/constants'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import {
  generateCombinedProducts,
  getConfigAttr,
  getConfigPrice,
  getIsAllOutOfStock,
  getIsOutOfStock,
  getMediaGalleryEntries,
  getOutOfStockVariants,
  getProductInfo,
  isProductConfigurable,
} from '@ninebot/core/src/utils/productUtils'
import { resolveCatchMessage, sleep } from '@ninebot/core/src/utils/util'
import { Dialog } from 'antd-mobile'
import { useTranslations } from 'next-intl'
import { createContext, useContext, useEffect, useMemo, useState } from 'react'

const ProductContext = createContext(null)

export function ProductProvider({ children, productDetails }) {
  // 加载状态
  const [isLoading, setIsLoading] = useState(true)
  // 产品状态
  const [productStatus, setProductStatus] = useState({
    popVisible: false,
    parentSku: '',
    sku: '',
    images: [],
    quantity: 1,
    servicePrice: 0,
  })

  // UI状态
  const [visibleAddCartPop, setVisibleAddCartPop] = useState(false)
  const [doorVisible, setDoorVisible] = useState(false)
  const [selectStore, setSelectStore] = useState(null) // 选中的门店
  const [selectCarModel, setSelectCarModel] = useState(null) // 选中的车辆信息
  const [selectCarModelPop, setSelectCarModelPop] = useState(false) // 车辆信息弹框
  const [isBuy, setIsBuy] = useState(false) // 是否是立即结算
  const [isLoadingPay, setLoadingPay] = useState(false) // 加购的loading
  const [carouselIndex, setCarouselIndex] = useState(0)
  const [carouselRender, setCarouselRender] = useState(true)

  const getI18nString = useTranslations('Common')
  const { openPage } = useNavigate()
  const toast = useToastContext()

  const dispatch = useAppDispatch()
  const { isLoggedIn, redirectLoginUrl } = useAuth()
  const { reportEvent } = useVolcAnalytics()

  const [addToCart, { isLoading: addCartLoading }] = useAddToCartMutation()
  const { fetchCheckoutCart } = useCheckoutCart()
  const [buyNowCheckout, { isLoading: buyNowLoading }] = useBuyNowCheckoutMutation()

  // 限流处理器
  const addToCartRateLimit = useRateLimitHandler({
    originalText: getI18nString('product_add_cart'),
  })

  const buyNowRateLimit = useRateLimitHandler({
    originalText: getI18nString('product_buy_now'),
  })

  const showProductRecommend = productDetails ? productDetails.related_products.length > 0 : true
  const productConfigurableOptions = useMemo(() => {
    // 处理顺序问题
    if (isProductConfigurable(productDetails)) {
      const options = productDetails.configurable_options
      const variants = productDetails.variants
      const obj = {}
      variants.forEach((item) => {
        item.attributes.forEach((attrItem) => {
          if (obj[attrItem.code]) {
            if (!obj[attrItem.code].includes(attrItem.value_index)) {
              obj[attrItem.code].push(attrItem.value_index)
            }
          } else {
            obj[attrItem.code] = [attrItem.value_index]
          }
        })
      })

      const opt = options.map((item) => {
        const option = []
        obj[item.attribute_code]?.forEach((code) => {
          item.values.forEach((val) => {
            if (val.value_index === code) {
              option.push(val)
            }
          })
        })
        item = { ...item, value: 1 }
        return {
          ...item,
          values: option,
        }
      })

      return opt
    } else {
      return null
    }
  }, [productDetails])

  const productConfigOptions = useMemo(() => {
    return productDetails ? productDetails.options : null
  }, [productDetails])

  const variants = useMemo(() => {
    return isProductConfigurable(productDetails) ? productDetails.variants : []
  }, [productDetails])

  // 门店自提
  const deliveryMethodPickup = useMemo(() => {
    let flag = false
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'delivery_method',
      )

      if (res) {
        const res1 = res.selected_options.find(
          (item) => item.value === 'delivery_method_store_pickup',
        )
        if (res1) {
          flag = true
        }
      }

      return flag
    }
  }, [productDetails])

  // 保险产品
  const isInsurance = useMemo(() => {
    const res = {
      is_insurance: false,
      link: '',
    }
    if (productDetails) {
      const insurance = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'is_insurance',
      )

      const url = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'insurance_link',
      )

      if (insurance) {
        res.is_insurance = insurance.value
      }

      if (url) {
        res.link = url.value
      }
    }
    return res
  }, [productDetails])

  // 是否有车型
  const bxCarModelList = useMemo(() => {
    let arr = []
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'bx_car_model',
      )

      if (res) {
        arr = res.selected_options
      }

      return arr
    }
  }, [productDetails])

  // 是否绑定车架号
  const isBoundCart = useMemo(() => {
    let flag = false
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'is_bound_cart',
      )

      if (res && res.value === '1') {
        flag = true
      }

      return flag
    }
  }, [productDetails])

  // 是否有优惠券
  const hasCoupon = useMemo(() => {
    let flag = false
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'product_receive_rule_ids',
      )

      // 产品是否有优惠券
      if (res && res.value) {
        flag = true
      }

      return flag
    }
  }, [productDetails])

  const isBuyNow = useMemo(() => {
    let flag = false
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'add_to_cart',
      )

      // 只有立即购买按钮
      if (res && res.value === '0') {
        flag = true
      }

      return flag
    }
  }, [productDetails])

  const safeguardItems = useMemo(() => {
    let arr = []
    if (productDetails) {
      const res = productDetails.custom_attributesV3.items.find(
        (item) => item.code === 'safeguard_clause',
      )
      if (res) {
        arr = res.selected_options
      }
    }
    return arr
  }, [productDetails])

  const isOutOfStockProductDisplayed = useMemo(() => {
    if (productDetails) {
      let totalVariants = 1
      const isConfigurable = isProductConfigurable(productDetails)
      if (productDetails.configurable_options && isConfigurable) {
        for (const option of productDetails.configurable_options) {
          const length = option.values.length
          totalVariants = totalVariants * length
        }

        return productDetails.variants.length === totalVariants
      }
    }
  }, [productDetails])

  const handleQtyChange = (quantity, max) => {
    if (quantity > max) {
      toast.show({
        content: getI18nString('purchase_limit_count_tip'),
      })
    }
    setProductStatus({
      ...productStatus,
      quantity,
    })
  }

  const handleSelectionChange = (item, optionId) => {
    const { optionCodes, optionSelections } = productStatus
    const selection = item.value_index
    // We must create a new Map here so that React knows that the value
    // of optionSelections has changed.
    const nextOptionSelections = new Map([...optionSelections])
    nextOptionSelections.set(optionId, selection)
    // Create a new Map to keep track of single selections with key as String
    const nextSingleOptionSelection = new Map()
    nextSingleOptionSelection.set(optionId, selection)
    appLocalStorage.removeItem('pdp_address')
    setProductStatus({
      ...productStatus,
      images: [],
    })
    handleProductConfigurable(optionCodes, nextOptionSelections, nextSingleOptionSelection)
  }

  const handleProductConfigurable = (
    optionCodes,
    nextOptionSelections,
    nextSingleOptionSelection,
    isFirst,
  ) => {
    const images = getMediaGalleryEntries(
      productDetails,
      optionCodes,
      nextOptionSelections,
      isFirst,
    )

    const price = getConfigPrice(productDetails, optionCodes, nextOptionSelections)

    const configInfo = getConfigAttr(productDetails, optionCodes, nextOptionSelections)

    // 显示N币逻辑
    let maxUsagencoins = null
    let paymentNcoin = false
    if (configInfo.product && configInfo.product.custom_attributesV3.items.length > 0) {
      // payment_method_ncoin 展示展示n样式
      // payment_method_cash_ncoin且最大n币数有值 展示现金加最大n币数
      // 展示正常价格
      // 最多可用使用N币数
      maxUsagencoins = configInfo.product.custom_attributesV3.items.find(
        (item) => item.code === 'max_usage_limit_ncoins',
      )

      const paymengMethod = configInfo.product.custom_attributesV3.items.find(
        (item) => item.code === 'paymeng_method',
      )

      let enabedNb = true
      if (paymengMethod && paymengMethod.selected_options) {
        paymengMethod.selected_options.forEach((item) => {
          if (['payment_method_ncoin'].includes(item.value)) {
            enabedNb = false
            paymentNcoin = true
          } else if (['payment_method_cach'].includes(item.value)) {
            enabedNb = false
          }
        })
      }

      if (!enabedNb) {
        maxUsagencoins = null
      }
    }

    const configName = configInfo.attributes.map((item) => {
      return item.label
    })

    const selected_configurable_option = configInfo.attributes.map((item) => {
      return {
        attribute_code: item.code,
        value_uid: item.uid,
      }
    })

    const info = getProductInfo(productDetails, optionCodes, nextOptionSelections)

    const isEverythingOutOfStock = getIsAllOutOfStock(productDetails)
    const isOutOfStock = getIsOutOfStock(productDetails, optionCodes, nextOptionSelections)

    const outOfStockVariants = getOutOfStockVariants(
      productDetails,
      optionCodes,
      nextSingleOptionSelection,
      nextOptionSelections,
      isOutOfStockProductDisplayed,
    )

    setCarouselRender(false)
    try {
      const data = {
        // quantity: productStatus.quantity, // 保留原有的数量
        // serviceItem: productStatus.serviceItem, // 保留原有的服务项
        optionCodes,
        salable_qty: configInfo.product.salable_qty,
        optionSelections: nextOptionSelections,
        price,
        selectOptionName: configName.join(','),
        images,
        image: info.image,
        id: info.id,
        sku: info.sku,
        maxUsagencoins,
        paymentNcoin,
        parent_sku: productDetails.sku,
        selected_configurable_option,
        isEverythingOutOfStock,
        isOutOfStock,
        outOfStockVariants,
      }

      setProductStatus({ ...productStatus, ...data })
    } catch (error) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error),
      })
    }

    sleep(500).then(() => {
      try {
        setSelectStore(null)
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error),
        })
      }

      setCarouselRender(true)
      if (!isFirst) {
        // 根据是否有视频来设置正确的主图索引
        const mainImageIndex = productDetails.video_url ? 1 : 0
        setCarouselIndex(mainImageIndex)
      }
    })
  }

  const handleUpdateService = (item) => {
    const price = productStatus.price
    if (productStatus.serviceItem) {
      setProductStatus({
        ...productStatus,
        price,
        servicePrice: 0,
        serviceItem: null,
      })
    } else {
      setProductStatus({
        ...productStatus,
        price,
        servicePrice: item.price,
        serviceItem: [item],
      })
    }
  }

  const onAddToCartBefore = async (type) => {
    reportEvent(
      type === 1 ? TRACK_EVENT.shop_details_cart_add_click : TRACK_EVENT.shop_details_buy_now_click,
      {
        sku_id: productStatus?.sku || productDetails?.sku || '',
      },
    )

    if (!isLoggedIn) {
      redirectLoginUrl()
      return
    }

    if (isInsurance.is_insurance === '1') {
      if (isInsurance.link) {
        Dialog.confirm({
          bodyClassName: 'custom-dialog-confirm',
          content: getI18nString('product_nine_app'),
          cancelText: getI18nString('product_cancel_app'),
          confirmText: getI18nString('product_open_app'),
          onConfirm: () => {
            openPage({
              route: ROUTE.webView,
              webViewUrl: isInsurance.link,
            })
          },
        })
      } else {
        toast.show({
          icon: 'fail',
          content: '保险产品，请提供第三方Url',
        })
      }

      return
    }

    if (type === 1) {
      // 加入购物车
      setIsBuy(false)
    } else {
      // 立即购买
      setIsBuy(true)
    }

    // 没有选择车架号，需选择
    if (isBoundCart) {
      if (!selectCarModel) {
        setSelectCarModelPop(true)
        appLocalStorage.setItem('carModelPay', 1)
        return
      }

      // 立即加购
      // 购买基础服务包
      onAddToCart(type)
      return
    }

    if (deliveryMethodPickup && !selectStore) {
      // 如果是自提 并未选择门店
      // 标记选择门店
      await appLocalStorage.setItem('select_product_option_open', 1)
      sleep(500).then(() => {
        setDoorVisible(true)
      })
    } else {
      // 选择规格，然后加入购物车
      setVisibleAddCartPop(true)
    }
  }

  const onAddToCart = async (type, params = {}) => {
    const {
      parent_sku,
      sku: curSku,
      quantity,
      selected_configurable_option,
      serviceItem,
    } = productStatus

    let input = {}
    let selectCarModelParams = {}

    if (isBoundCart) {
      if (selectCarModel || params.selectCarModel) {
        const carModel = selectCarModel || params.selectCarModel
        const { vin, manufacturer, gpsNo, cycleNo, snNo, productImage, decivceName } = carModel
        selectCarModelParams = {
          car_vin: {
            vin: vin || '',
            manufacturer,
            gpsNo: gpsNo || '',
            cycleNo: cycleNo || '',
            snNo: snNo || '',
            device_image: productImage,
            device_name: decivceName,
          },
        }
      } else {
        setSelectCarModelPop(true)
        return
      }
    }

    // 门店自提
    let selectedStoreInfo = {}
    if (deliveryMethodPickup) {
      if (!selectStore) {
        setVisibleAddCartPop(false)
        setDoorVisible(true)
        appLocalStorage.setItem('select_product_option_open', 1)
        return
      } else {
        selectedStoreInfo = {
          selected_store_info: {
            store_id: selectStore.store_id,
            store_code: selectStore.store_code,
          },
        }
      }
    }

    // 判断选择items.options是否都是必须选择的
    let isOptionsSelected = false
    let optionsSelectedTitle = ''
    productDetails?.options?.forEach(function (item) {
      if (item.required) {
        isOptionsSelected = true
        optionsSelectedTitle = item.title
      }
    })

    if (isOptionsSelected && !serviceItem) {
      toast.show({
        icon: 'info',
        content: '请' + optionsSelectedTitle,
      })
      return
    }

    const serviceOptionItem = []
    if (serviceItem) {
      serviceItem.forEach((item) => {
        serviceOptionItem.push({
          value: item.value,
          option_id: item.option_id,
        })
      })
    }

    if (productDetails.__typename === 'VirtualProduct') {
      input = {
        parent_sku: productDetails.sku,
        sku: productDetails.sku,
        quantity: quantity || 1,
        ...selectCarModelParams,
        ...selectedStoreInfo,
      }
    } else if (productDetails.__typename === 'ConfigurableProduct') {
      input = {
        parent_sku,
        sku: curSku,
        quantity: quantity || 1,
        selected_configurable_option,
        custom_options: serviceOptionItem,
        ...selectedStoreInfo,
      }
    } else if (productDetails.__typename === 'BundleProduct') {
      // 选择车架号
      const selected_bundle_option = []
      productDetails.items.forEach((item) => {
        const values = []
        item.options.forEach((option) => {
          values.push(option.id)
        })
        selected_bundle_option.push({
          option_id: item.option_id,
          value_id: values,
          quantity: item.options[0].quantity,
        })
      })
      input = {
        parent_sku: productDetails.sku,
        sku: productDetails.sku,
        quantity: quantity || 1,
        selected_bundle_option,
        ...selectCarModelParams,
        ...selectedStoreInfo,
      }
    } else if (productDetails.__typename === 'SimpleProduct') {
      input = {
        parent_sku: productDetails.sku,
        sku: productDetails.sku,
        quantity: quantity || 1,
        custom_options: serviceOptionItem,
        ...selectedStoreInfo,
      }
    }

    let response = {}
    setLoadingPay(true)
    try {
      if (type === 2) {
        response = await buyNowCheckout({
          input: [input],
        }).unwrap()
      } else {
        response = await addToCart({
          input: [input],
        }).unwrap()
        fetchCheckoutCart()
      }
      setVisibleAddCartPop(false)

      if (response.addProductsToShippingCart) {
        // 重置数量
        setProductStatus({
          ...productStatus,
          quantity: 1,
        })
        toast.show({
          icon: 'success',
          content: getI18nString('product_add_cart_success'),
        })
        setLoadingPay(false)
      } else if (response.buyNowToCheckout) {
        // 不关闭当前弹框
        setProductStatus({
          ...productStatus,
          quantity: 1,
        })
        sleep(500).then(() => {
          setLoadingPay(false)
          openPage({
            route: ROUTE.checkout,
          })
        })
      }
    } catch (error) {
      setVisibleAddCartPop(false)
      setLoadingPay(false)
      await sleep(500)

      // 精准限流
      if (error?.type === PRECISE_RATE_LIMIT) {
        if (type === 1) {
          addToCartRateLimit.startRateLimit(error.retryMs)
        } else {
          buyNowRateLimit.startRateLimit(error.retryMs)
        }
      }

      // 如果不是限流错误，显示普通错误提示
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error),
      })
    }
  }

  useEffect(() => {
    if (productDetails) {
      const optionCodes = new Map()
      const nextOptionSelections = new Map()
      const nextSingleOptionSelection = new Map()

      if (isProductConfigurable(productDetails)) {
        let initSelectOption = null
        let isFind = false

        const productArr = generateCombinedProducts(productConfigurableOptions)
        for (const option of productConfigurableOptions) {
          optionCodes.set(option.attribute_id, option.attribute_code)
        }

        for (const productItem of productArr) {
          for (const variantItem of variants) {
            const attributesArr = []
            if (variantItem.product.stock_status === 'IN_STOCK') {
              for (const attributeItem of variantItem.attributes) {
                attributesArr.push(attributeItem.value_index)
              }
            }

            if (attributesArr.join('-') === productItem.value && !isFind) {
              initSelectOption = variantItem
              isFind = true
              break
            }
          }

          if (isFind) {
            break
          }
        }

        // 选中了第一个有库存的产品
        if (isFind) {
          initSelectOption.attributes.forEach((item) => {
            productConfigurableOptions.forEach((option) => {
              option.values.forEach((i) => {
                if (item.value_index === i.value_index) {
                  nextSingleOptionSelection.clear()
                  nextOptionSelections.set(option.attribute_id, i.value_index)
                  nextSingleOptionSelection.set(option.attribute_id, i.value_index)
                }
              })
            })
          })
        }

        handleProductConfigurable(
          optionCodes,
          nextOptionSelections,
          nextSingleOptionSelection,
          true,
        )
      } else {
        handleProductConfigurable(optionCodes, nextOptionSelections, nextSingleOptionSelection)
      }

      setIsLoading(false)

      // 清除选择的地址
      appLocalStorage.removeItem('pdp_address')
      appLocalStorage.removeItem('pdp_user_location')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productDetails])

  const termsOfSale = useMemo(() => {
    return productDetails?.custom_attributesV3.items.find((item) => item.code === 'terms_of_sale')
  }, [productDetails])

  /**
   * 为适配立即购买到结算页数据
   * 每次进入页面都清除 checkout 数据
   */
  useEffect(() => {
    dispatch(resetCheckout())
  }, [dispatch])

  const value = {
    // 加载状态
    isLoading,
    // 产品数据
    productDetails,
    productStatus,
    productConfigurableOptions,
    productConfigOptions,
    variants,
    showProductRecommend,

    // 加载状态
    isLoadingPay,
    addCartLoading,
    buyNowLoading,

    // 购物车相关
    onAddToCart,
    onAddToCartBefore,
    isBoundCart,
    isBuyNow,
    isBuy,
    hasCoupon,

    // 限流处理
    addToCartRateLimit,
    buyNowRateLimit,

    // 配送方式
    deliveryMethodPickup,

    // UI状态
    visibleAddCartPop,
    setVisibleAddCartPop,
    doorVisible,
    setDoorVisible,
    carouselRender,
    carouselIndex,

    // 选择相关
    handleSelectionChange,
    handleQtyChange,
    handleUpdateService,

    // 门店和车型
    selectStore,
    setSelectStore,
    selectCarModel,
    setSelectCarModel,
    selectCarModelPop,
    setSelectCarModelPop,
    bxCarModelList,

    // 服务项目
    safeguardItems,
    termsOfSale,
    getI18nString,
    isLoggedIn,
  }

  return <ProductContext.Provider value={value}>{children}</ProductContext.Provider>
}

export function useProduct() {
  const context = useContext(ProductContext)
  if (!context) {
    throw new Error('useProduct must be used within a ProductProvider')
  }
  return context
}
