'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import { mergeStyles, TRACK_EVENT, useAuth, useVolcAnalytics } from '@ninebot/core'

import { CustomCheckbox } from '@/components'

interface CategoryFilterProps {
  sort: Record<string, 'ASC' | 'DESC'>
  setSort: (sort: Record<string, 'ASC' | 'DESC'>) => void
  isDigital: boolean
  onlyMyCar: boolean
  setOnlyMyCar: (onlyMyCar: boolean) => void
}

export default function CategoryFilter({
  sort,
  setSort,
  isDigital,
  onlyMyCar,
  setOnlyMyCar,
}: CategoryFilterProps) {
  const getI18nString = useTranslations('Common')
  const { isLoggedIn } = useAuth()
  const { reportEvent } = useVolcAnalytics()
  const PRODUCT_LIST_FILTER_OPTIONS = [
    {
      label: getI18nString('position'),
      value: 1,
      key: 'position',
    },
    {
      label: getI18nString('price'),
      value: 2,
      key: 'price',
    },
  ]

  const initialSortKey = useMemo(() => Object.keys(sort)[0] || 'position', [sort])
  const [sortType, setSortType] = useState(sort[initialSortKey] || 'ASC')
  const [activeOption, setActiveOption] = useState(initialSortKey)

  const handleOptionPress = useCallback(
    (option: { key: string }) => {
      if (initialSortKey === 'position' && option.key === 'position') {
        return
      }

      setActiveOption(option.key)

      let currentSortType = sortType
      if (option.key === 'price') {
        currentSortType = sortType === 'DESC' ? 'ASC' : 'DESC'
        setSortType(currentSortType)
      } else {
        setSortType('ASC')
        currentSortType = 'ASC'
      }

      setSort({
        [option.key]: currentSortType,
      })

      reportEvent(TRACK_EVENT.shop_sort_option_click)
    },
    [initialSortKey, setSort, sortType, reportEvent],
  )

  useEffect(() => {
    if (initialSortKey !== activeOption) {
      setSortType(sort[initialSortKey] || 'ASC')
      setActiveOption(initialSortKey)
    }
  }, [initialSortKey, activeOption, sort])

  return (
    <div className="mt-[4px] flex justify-between">
      <div className="mt-[4px] flex flex-row items-center">
        {PRODUCT_LIST_FILTER_OPTIONS.map((option) => (
          <div
            key={option.key}
            className={
              activeOption === option.key
                ? 'mr-4 flex flex-row items-center justify-center rounded-[100px] border-[1px] border-[#DA291C] bg-[#DA291C0D] px-[12px] py-[5.5px]'
                : 'mr-4 flex flex-row items-center justify-center rounded-[100px] border-[1px] border-transparent bg-[#F3F3F4] px-[12px] py-[5.5px]'
            }
            onClick={() => handleOptionPress(option)}>
            <div
              className={
                activeOption === option.key
                  ? 'text-[11px] leading-[12px] text-[#DA291C]'
                  : 'text-[11px] leading-[12px] text-[#444446]'
              }>
              {option.label}
            </div>
            {['price'].includes(option.key) && (
              <div className="ml-[6px] flex h-[12px] flex-col justify-between">
                <div
                  className={mergeStyles([
                    'h-0 w-0 border-b-[5px] border-l-[4px] border-r-[4px] border-l-transparent border-r-transparent',
                    sort[option.key] === 'ASC' ? 'border-b-[#DA291C]' : 'border-b-[#BBBBBD]',
                  ])}
                />
                <div
                  className={mergeStyles([
                    'h-0 w-0 border-l-[4px] border-r-[4px] border-t-[5px] border-l-transparent border-r-transparent',
                    sort[option.key] === 'DESC' ? 'border-t-[#DA291C]' : 'border-t-[#BBBBBD]',
                  ])}
                />
              </div>
            )}
          </div>
        ))}
      </div>
      {isDigital && isLoggedIn && (
        <div className="flex flex-row items-center gap-[8px]">
          <CustomCheckbox
            checked={onlyMyCar}
            onChange={(checked) => {
              setOnlyMyCar(checked)
            }}>
            <div className="font-miSansRegular330 text-[14px] leading-8 text-[#0F0F0F]">
              {getI18nString('only_my_car')}
            </div>
          </CustomCheckbox>
        </div>
      )}
    </div>
  )
}
