const pkg = require('../package.json')
const ecosystemBaseConfig = require('./ecosystem-base.config')
const fs = require('node:fs')
const path = require('node:path')
const os = require('node:os')

require('dotenv').config({
  path: path.resolve(__dirname, '..', process.env.ENV_FILE || '.env'), // 默认加载 .env
})

// 获取构建 ID
const BUILD_ID = fs.readFileSync(path.join(__dirname, '../.next/BUILD_ID'), 'utf-8').trim()
// 应用名称
const NAME = BUILD_ID ? BUILD_ID : `${pkg.name}-${pkg.version}`

// 1. 获取服务器总内存（转换为 GB）
const totalMemoryGB = os.totalmem() / 1024 / 1024 / 1024
// 2. 计算可用内存（总内存 - 2GB，至少保留 1GB）
const reservedMemoryGB = 2
const availableMemoryGB = Math.max(totalMemoryGB - reservedMemoryGB, 1)
// 3. 获取 CPU 核心数（至少保留 1 个核心）
const cpuCores = Math.max(os.cpus().length, 1)
// 4. 最大可用 cpu 核心数量
const maxCpuCores = Math.max(cpuCores - 1, 1)
// 5. 计算每个实例的内存限制（转换为 MB）
const maxMemoryPerInstanceMB = Math.floor((availableMemoryGB / maxCpuCores) * 1024)

// 是否开启最大性能
const isMaxPerformance = process.env.PM2_MAX === '1'

module.exports = {
  apps: [
    {
      ...ecosystemBaseConfig,
      name: NAME,
      script: '../../node_modules/next/dist/bin/next',
      args: 'start',
      namespace: 'production',
      instances: isMaxPerformance ? maxCpuCores : 3, // 实例数 = CPU核心数 - 1
      max_memory_restart: isMaxPerformance ? `${maxMemoryPerInstanceMB}M` : '200M', // 内存限制
      env: {
        // NODE_ENV: "production",
        PORT: 3081,
      },
      // 错误日志文件
      error_file: `./deploy/logs/${NAME}-err.log`,
      // 正常日志文件
      out_file: `./deploy/logs/${NAME}-out.log`,
    },
  ],
}
